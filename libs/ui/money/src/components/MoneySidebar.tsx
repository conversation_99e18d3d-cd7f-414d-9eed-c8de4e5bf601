import React from 'react';
import { MoneyBlocks } from './MoneyBlocks';
import { ContentManager, WordpressPost, WordpressSidebar } from '@benzinga/content-manager';
import { getSessionSingleton } from '@benzinga/session';
// import { AmplyWidget } from '@benzinga/ads';
// import { appEnvironment, appName } from '@benzinga/utils';
import { NoFirstRender } from '@benzinga/hooks';
import { LayoutAdmin } from '@benzinga/blocks';

export interface MoneySidebarProps {
  post?: WordpressPost | null;
  sidebar: WordpressSidebar;
  isHomePage?: boolean;
}

export const MoneySidebar: React.FC<MoneySidebarProps> = ({ isHomePage = false, post, sidebar }) => {
  return (
    <div className="sidebar">
      <NoFirstRender>
        <React.Suspense>{sidebar && <LayoutAdmin post={sidebar} />}</React.Suspense>
      </NoFirstRender>
      <MoneyBlocks blocks={sidebar?.blocks} isHomePage={isHomePage} post={post} type="sidebar" />
      {/* {!appEnvironment().isApp(appName.india) && <AmplyWidget id="amply-widget-sidebar" variant="sidebar" />} */}
    </div>
  );
};

export const getMoneySidebar = async (post_id: number) => {
  const session = getSessionSingleton();
  const contentManager = session.getManager(ContentManager);
  const pageRes = await contentManager.getPage(post_id);
  return pageRes?.ok ?? null;
};
