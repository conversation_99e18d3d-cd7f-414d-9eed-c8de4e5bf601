import React from 'react';
import styled from '@benzinga/themetron';
import { MoneyBlock } from './MoneyBlock';
import { WordpressPost } from '@benzinga/content-manager';
import type { BlockProps } from '@benzinga/blocks';
import { findSpecificBlocks } from '@benzinga/blocks-utils';
import { ErrorBoundary } from '@benzinga/core-ui';
import LazyLoad from 'react-lazyload';
import { useBenzingaEdge } from '@benzinga/edge';
import StickyBox from 'react-sticky-box';

export interface MoneyBlocksProps {
  blocks?: any[];
  campaigns?: any;
  post?: WordpressPost | null;
  insertBeforeBlock?: {
    blockName: string;
    position?: number;
    componentToInsert: React.ReactElement;
  };
  insertAfterBlocks?: {
    blockName: string;
    position?: number;
    componentToInsert: React.ReactElement;
  }[];
  lazyLoadEnabled?: boolean;
  disallowLazyLoadBlockList?: string[];
  lazyLoadStartIndex?: number;
  type?: 'default' | 'sidebar';
  isHomePage?: boolean; // Flag to indicate this is the home page sidebar
}

export const MoneyBlocks: React.FC<MoneyBlocksProps> = ({
  blocks,
  campaigns,
  disallowLazyLoadBlockList,
  insertAfterBlocks,
  insertBeforeBlock,
  isHomePage = false,
  lazyLoadEnabled = false,
  lazyLoadStartIndex,
  post,
  type,
}) => {
  const { adLightEnabled } = useBenzingaEdge();
  if (!Array.isArray(blocks)) {
    return null;
  }

  const insertComponent = (
    blocks: BlockProps[],
    blockName: string,
    position = 0,
    currentBlock: BlockProps,
    componentToInsert: React.ReactElement,
  ) => {
    if (currentBlock.blockName !== blockName) return null;
    const foundBlocks = findSpecificBlocks(blocks, blockName);
    const result = foundBlocks[position];
    if (result) {
      return componentToInsert;
    }
    return null;
  };

  const renderBlock = (block: BlockProps, i: number) => {
    // Check if this is the first raptive ad block on the home page sidebar for Ad-light users
    const isFirstRaptiveAd =
      isHomePage &&
      adLightEnabled &&
      type === 'sidebar' &&
      block?.blockName === 'acf/raptive-ad-placement' &&
      block?.attrs?.data?.type === 'static-sidebar' &&
      i === 0; // First block in the sidebar

    const blockContent = (
      <ErrorBoundary
        disableFallback={true}
        key={`money-blocks-wrapper-item-${block?.blockName}-${i}`}
        name={`money-block-${block?.blockName}`}
      >
        <React.Suspense fallback={<div />}>
          <>
            {insertBeforeBlock?.blockName &&
              insertComponent(
                blocks,
                insertBeforeBlock?.blockName,
                insertBeforeBlock?.position,
                block,
                insertBeforeBlock?.componentToInsert,
              )}
            <React.Suspense fallback={<div />}>
              <MoneyBlock block={block} campaigns={campaigns} post={post} type={type} />
            </React.Suspense>
            {Array.isArray(insertAfterBlocks) &&
              insertAfterBlocks.map(insertAfterBlock => (
                <React.Fragment key={`${insertAfterBlock.blockName}-${insertAfterBlock?.position}`}>
                  {insertAfterBlock?.blockName
                    ? insertComponent(
                        blocks,
                        insertAfterBlock?.blockName,
                        insertAfterBlock?.position,
                        block,
                        insertAfterBlock?.componentToInsert,
                      )
                    : null}
                </React.Fragment>
              ))}
          </>
        </React.Suspense>
      </ErrorBoundary>
    );

    // For Ad-light users on home page, make the first static-sidebar raptive ad sticky
    if (isFirstRaptiveAd) {
      return (
        <StickyBox offsetTop={0} offsetBottom={0}>
          {blockContent}
        </StickyBox>
      );
    }

    return blockContent;
  };

  const shouldLazyLoad = lazyLoadEnabled && typeof lazyLoadStartIndex === 'number';

  return (
    <MoneyBlocksWrapper className="money-blocks-wrapper">
      {blocks?.map((block: BlockProps, i: number) => {
        if (shouldLazyLoad && i > lazyLoadStartIndex && !disallowLazyLoadBlockList?.includes(block?.blockName)) {
          return (
            <LazyLoad height={150} key={`money-blocks-wrapper-lazy-${block?.blockName}-${i}`} offset={220} once>
              {renderBlock(block, i)}
            </LazyLoad>
          );
        } else {
          return renderBlock(block, i);
        }
      })}
    </MoneyBlocksWrapper>
  );
};

export const MoneyBlocksWrapper = styled.div`
  .block {
    margin: 1rem 0;
    &:first-of-type {
      margin-top: 0;
    }
    &:last-of-type {
      margin-bottom: 0;
    }
  }
  .right {
    float: right;
    margin-left: 0.5rem;
  }
  .left {
    float: left;
    margin-right: 0.5rem;
  }
  .center {
    text-align: center;
    margin: auto;
    > figure,
    > img,
    > div {
      margin: auto;
    }
  }
  h1,
  h2 {
    &.core-block {
      margin-top: 2rem;
      margin-bottom: 1rem;
    }
  }
  h1 {
    &.core-block {
      font-size: ${({ theme }) => theme.fontSize['4xl']};
    }
  }
  h2 {
    &.core-block {
      font-size: ${({ theme }) => theme.fontSize['3xl']};
      color: ${({ theme }) => theme.colors.foregroundInactive};
    }
  }
  h3 {
    &.core-block {
      font-size: ${({ theme }) => theme.fontSize['2xl']};
      margin: 1rem 0px 0.5rem 0px;
    }
  }
  h4 {
    &.core-block {
      font-size: ${({ theme }) => theme.fontSize['1xl']};
      margin: 1rem 0px 0.5rem 0px;
    }
  }
  ul {
    &.core-block {
      list-style: disc;
      margin-left: 2rem;
      margin-bottom: 2rem;
      ul,
      ol {
        margin-bottom: 0;
      }
    }
  }
  ol {
    &.core-block {
      list-style: decimal;
      margin-left: 2rem;
      margin-bottom: 2rem;
      li {
        margin-bottom: 0.2rem;
      }
      ul,
      ol {
        margin-bottom: 0;
      }
    }
  }
  .wp-block-spacer {
    clear: both;
  }
  p {
    &.core-block {
      font-size: 1.1rem;
    }
  }
  .core-block {
    a {
      color: #2ca2d1;
    }
  }
  .alignright {
    float: right;
    margin-left: 18px;
  }
  .image-wrapper {
    margin-bottom: 1rem;
  }
  .wp-block-table {
    overflow-x: auto;
  }
  .wp-element-caption {
    border-bottom: 1px solid #d6d6d6;
    border-top: 1px solid #d6d6d6;
    background-color: #f4f4f4;
    font-size: ${({ theme }) => theme.fontSize.base};
    text-align: center;
    margin-top: -1rem;
    margin-bottom: 1rem;
  }
  table.core-block,
  figure.core-block table,
  figure.wp-block-table table {
    width: 100%;
    /* white-space: nowrap; // this does not work */

    th {
      background-color: ${({ theme }) => theme.colorPalette.blue500};
      color: #ffffff;
      padding: 4px 8px;
      font-size: 14px;
    }

    tbody {
      thead tr,
      > tr:first-of-type {
        th {
          padding: 4px 8px;
          font-size: 14px;
          font-weight: 600;
          /* border-right: 1px solid #d6d6d6;
          &:first-child {
            border-left: 1px solid #d6d6d6;
          } */
        }
      }

      tr {
        /* &:first-child {
          border-top: 1px solid #d6d6d6;
        } */
        &:nth-child(even) {
          background: #f9f9f9;
          /* background: #f1f1f1; */
        }
      }
      td {
        /* border-right: 1px solid #d6d6d6; */
        /* border-bottom: 1px solid #d6d6d6; */
        padding: 4px 8px;
        /* &:first-child {
          border-left: 1px solid #d6d6d6;
        } */
      }
    }
  }
  .blocks-gallery-grid,
  .wp-block-gallery {
    display: flex;
    flex-wrap: wrap;
    list-style-type: none;
    padding: 0;
    margin: 0;
    .blocks-gallery-item {
      margin: 0 1em 1em 0;
      display: flex;
      flex-grow: 1;
      flex-direction: column;
      justify-content: center;
      position: relative;
      width: calc(50% - 1em);
      img {
        display: block;
        max-width: 100%;
      }
      figure {
        display: flex;
        align-items: flex-end;
        justify-content: flex-start;
      }
    }
    @media (min-width: 600px) {
      &.columns-3 {
        .blocks-gallery-grid {
          .blocks-gallery-item {
            width: calc(32% - 0.66667em);
            margin-right: 1em;
          }
        }
      }
    }
  }
  .wp-block-quote {
    padding: 0 1em;
    margin: 0px 0px 2rem;
    line-height: 1.6;
    color: #2d2d2d;
    border-left: 2px solid ${({ theme }) => theme.colorPalette.blue800};
    font-style: italic;
  }
  > div {
    > img,
    > figure {
      max-width: 100%;
    }
    > table,
    > figure table,
    .core-block-table-wrapper table {
      margin: 1rem 0;
      width: 100%;
      max-width: 100%;
      /* white-space: nowrap; // this does not work */

      th {
        background-color: ${({ theme }) => theme.colorPalette.blue500};
        color: #ffffff;
        padding: 4px 8px;
        text-align: left;
      }

      tbody {
        thead tr,
        > tr:first-of-type {
          th {
            padding: 4px 8px;
            font-size: 14px;
            /* border-right: 1px solid #d6d6d6;
            &:first-child {
              border-left: 1px solid #d6d6d6;
            } */
          }
        }

        tr {
          /* &:first-child {
            border-top: 1px solid #d6d6d6;
          } */
          &:nth-child(even) {
            background: #f9f9f9;
            /* background: #f1f1f1; */
          }
        }
        td {
          /* border-right: 1px solid #d6d6d6; */
          /* border-bottom: 1px solid #d6d6d6; */
          padding: 4px 8px;
          font-size: 14px;
          /* &:first-child {
            border-left: 1px solid #d6d6d6;
          } */
        }
      }
    }
    > h1,
    > h2 {
      margin-top: 2rem;
      margin-bottom: 1rem;
    }
    > h1 {
      font-size: ${({ theme }) => theme.fontSize['4xl']};
    }
    > h2 {
      font-size: ${({ theme }) => theme.fontSize['3xl']};
      color: ${({ theme }) => theme.colors.foregroundInactive};
    }
    > h3 {
      font-size: ${({ theme }) => theme.fontSize['2xl']};
      margin: 1rem 0px 0.5rem 0px;
    }
    > h4 {
      font-size: ${({ theme }) => theme.fontSize['1xl']};
      margin: 1rem 0px 0.5rem 0px;
    }
    > ul {
      list-style: disc;
      margin-left: 2rem;
      margin-bottom: 2rem;
    }
    > ol {
      list-style: decimal;
      margin-left: 2rem;
      margin-bottom: 2rem;
      li {
        margin-bottom: 0.2rem;
      }
    }
    > p {
      font-size: 1.1rem;
    }
    a {
      color: #2ca2d1;
    }
  }
  .bz-campaign {
    hr {
      background: #5294c1;
      border: 0;
      height: 1px;
      margin-bottom: 26px;
      padding: 0;
    }
  }
`;
