'use client';
import React, { startTransition, useMemo, useRef } from 'react';
import LazyLoad from 'react-lazyload';
import StickyBox from 'react-sticky-box';
import { debounce } from '@benzinga/utils';
import styled from '@benzinga/themetron';
import type { Breadcrumb, LayoutHeaderOptions, LayoutNotification, Term } from '@benzinga/content-manager';
import { isMobile } from '@benzinga/device-utils';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import Hooks from '@benzinga/hooks';
import classNames from 'classnames';
import { SectionSubtitle } from '../Page/SectionSubtitle';
import { Breadcrumbs } from '../Breadcrumbs';

const FollowChannelButton = React.lazy(() =>
  import('../FollowButtons').then(module => ({
    default: module.FollowChannelButton,
  })),
);

export interface LayoutProps {
  alignTitleAboveMain?: boolean;
  breadcrumbs?: Breadcrumb[];
  categoryId?: string | null;
  className?: string;
  description?: string;
  hideSidebar?: boolean;
  layoutAbove?: React.ReactElement | boolean;
  layoutBelow?: React.ReactElement | boolean;
  layoutFooter?: React.ReactElement | boolean;
  layoutHeader?: React.ReactElement | boolean;
  layoutHeaderOptions?: LayoutHeaderOptions;
  layoutMain: React.ReactElement;
  layoutMainClassName?: string;
  layoutLeftSidebar?: React.ReactElement | boolean;
  layoutSidebar?: React.ReactElement | boolean;
  maxWidth?: string;
  notification?: LayoutNotification;
  showFollowChannelButton?: boolean;
  sidebarSettings?: SidebarSettings;
  subtitle?: string;
  term?: Term;
  title?: string;
  width?: string;
  url?: string;
}

export interface SidebarSettings {
  offsetTop?: number;
  offsetBottom?: number;
  visibleOnMobile?: boolean;
  width?: number;
}

// Eventually, include the Header
export const Layout: React.FC<LayoutProps> = ({
  alignTitleAboveMain,
  breadcrumbs,
  categoryId,
  className,
  description,
  hideSidebar,
  layoutAbove,
  layoutBelow,
  layoutFooter,
  layoutHeader,
  layoutHeaderOptions,
  layoutLeftSidebar,
  layoutMain,
  layoutMainClassName,
  layoutSidebar,
  maxWidth = '7xl',
  notification,
  showFollowChannelButton = true,
  sidebarSettings = {},
  subtitle,
  term,
  title,
  url,
  width,
}) => {
  const [isSidebarVisible, setSidebarVisibility] = React.useState(true);

  // By Default, we set the max-width to max-w-7xl
  // Some use-cases (like SEC Filings) need a more full screen view for convenience
  // There's room here to expand this concept or change how layouts are defined
  let layoutClasses = width === 'full' ? 'layout-content-container' : 'layout-content-container lg:px-0 mx-auto px-4 ';
  layoutClasses += width === 'full' ? 'w-screen' : 'layout-container max-w-' + maxWidth;
  const layoutMaxWidth = width === 'full' ? 'max-width: 2000px;' : '';
  const mainWrapperClasses = `main-content-container${layoutLeftSidebar ? ' has-left-sidebar' : ''} ${
    width === 'full' ? 'w-full' : 'layout-container mx-auto'
  }`;

  const { entry, isStickyNavbar } = Hooks.useDetectStickyHeader();
  const leftSidebarOffsetTop = entry?.boundingClientRect?.height
    ? Math.abs(entry?.boundingClientRect?.height) + 10
    : 132;

  const handleSidebarVisibility = React.useCallback(() => {
    if (!isMobile()) {
      startTransition(() => {
        setSidebarVisibility(true);
      });
    } else {
      startTransition(() => {
        setSidebarVisibility(false);
      });
    }
  }, []);

  const debouncedSidebarVisibility = React.useMemo(() => {
    return debounce(handleSidebarVisibility, 50);
  }, [handleSidebarVisibility]);

  React.useEffect(() => {
    handleSidebarVisibility();
  }, [handleSidebarVisibility]);

  Hooks.useEventListener('resize', debouncedSidebarVisibility);

  const isLeftSidebarVisible = useMemo(() => {
    return layoutLeftSidebar && isSidebarVisible;
  }, [isSidebarVisible, layoutLeftSidebar]);

  const showLayoutAbove =
    layoutAbove || title || subtitle || (breadcrumbs && breadcrumbs.length > 0) || notification || description;

  return (
    <LayoutWrapper className={className}>
      {layoutHeader && (
        <LayoutFlex>
          <LayoutHeader options={layoutHeaderOptions}>{layoutHeader}</LayoutHeader>
        </LayoutFlex>
      )}
      <LayoutContainer className={layoutClasses} layoutMaxWidth={layoutMaxWidth}>
        {showLayoutAbove && (
          <LayoutAbove offsetLeft={alignTitleAboveMain && isLeftSidebarVisible ? 250 : 0}>
            <div className="flex flex-col md:flex-row items-start md:items-center gap-2 md:gap-8">
              {title && <LayoutHeadline level={1} text={title} url={url} />}
              {categoryId && showFollowChannelButton && (
                <FollowChannelButton categoryId={categoryId} termName={term?.name} />
              )}
            </div>
            {subtitle && <LayoutHeadline className="mb-4 layout-subtitle" level={2} text={subtitle} />}
            {((breadcrumbs && breadcrumbs.length > 0) || notification) && (
              <div className="related-info-container">
                {breadcrumbs && breadcrumbs.length > 1 && <Breadcrumbs data={breadcrumbs} />}
                {notification && (
                  <NotificationLink href={notification.link} target="_blank">
                    {notification.label}
                  </NotificationLink>
                )}
              </div>
            )}
            {description ? <SectionSubtitle className="section-description">{description}</SectionSubtitle> : null}
            {layoutAbove}
          </LayoutAbove>
        )}
        <div className={mainWrapperClasses}>
          <LayoutFlex>
            {layoutLeftSidebar && (
              <StickyBox
                offsetBottom={0}
                offsetTop={0}
                style={{ display: 'flex', position: 'sticky', top: isStickyNavbar ? leftSidebarOffsetTop : 20 }}
              >
                <LayoutLeftSidebar visible={isLeftSidebarVisible ?? false}>{layoutLeftSidebar}</LayoutLeftSidebar>
              </StickyBox>
            )}
            {sidebarSettings?.visibleOnMobile && layoutLeftSidebar && !isLeftSidebarVisible && (
              <LayoutLeftSidebar sidebarWrapperClass="full-width" visible={true}>
                {layoutLeftSidebar}
              </LayoutLeftSidebar>
            )}
            <LayoutMain className={layoutMainClassName}>{layoutMain}</LayoutMain>
            {isSidebarVisible && layoutSidebar && !hideSidebar && (
              <StickyBox className="layout-sidebar-sticky-box" offsetBottom={0} offsetTop={0}>
                <LayoutSidebar visible={isSidebarVisible} width={sidebarSettings?.width}>
                  {layoutSidebar && (
                    <LazyLoad once>
                      <>{layoutSidebar}</>
                    </LazyLoad>
                  )}
                </LayoutSidebar>
              </StickyBox>
            )}
            {!isSidebarVisible && sidebarSettings?.visibleOnMobile && (
              <LayoutSidebar visible={true}>
                {layoutSidebar && (
                  <LazyLoad once>
                    <>{layoutSidebar}</>
                  </LazyLoad>
                )}
              </LayoutSidebar>
            )}
          </LayoutFlex>
        </div>
        <LayoutBelow>{layoutBelow}</LayoutBelow>
      </LayoutContainer>
      {layoutFooter && (
        <LazyLoad offset={100} once>
          <LayoutFlex>
            <LayoutFooter>{layoutFooter}</LayoutFooter>
          </LayoutFlex>
        </LazyLoad>
      )}
      {/* <LazyLoad offset={100} once>
        <Footer />
      </LazyLoad> */}
    </LayoutWrapper>
  );
};

export const LayoutFlex: React.FC<React.PropsWithChildren> = ({ children }) => {
  return <div className="layout-flex flex flex-col w-full items-start relative">{children}</div>;
};

const LayoutHeader: React.FC<React.PropsWithChildren<{ options?: LayoutHeaderOptions }>> = React.memo(
  ({ children, options }) => {
    const [isHeaderFixed, setIsHeaderFixed] = React.useState(false);
    const [offsetTop, setOffsetTop] = React.useState(0);
    const [blockHeight, setBlockHeight] = React.useState(0);
    const blockRef = useRef<HTMLInputElement>(null);

    const style = {};
    let variant = 'default';
    if (options?.background_color) style['backgroundColor'] = options?.background_color;
    if (options?.full_width) {
      variant = 'full-width';
    }

    Hooks.useEffectDidMount(() => {
      updateOffset();
    });

    Hooks.useEventListener('scroll', () => {
      if (!options?.sticky || isMobile()) return;
      if (window.scrollY >= 132 && !isHeaderFixed) {
        updateOffset();
        setIsHeaderFixed(true);
      } else if (window.scrollY < 132 && isHeaderFixed) {
        setIsHeaderFixed(false);
      }
    });

    const updateOffset = () => {
      const headerElement = document.querySelector('#navigation-header');

      if (headerElement) {
        setOffsetTop(headerElement.clientHeight - 16);
      }

      const headerContentElement = blockRef.current;
      if (headerContentElement) {
        setBlockHeight(headerContentElement.clientHeight);
      }
    };

    if (!children) {
      return <></>;
    }

    return (
      <>
        {isHeaderFixed ? <div style={{ height: `${blockHeight}px` }}></div> : null}
        <div
          className={classNames(`layout-header w-full py-4 overflow-hidden ${variant}`, { fixed: isHeaderFixed })}
          ref={blockRef}
          style={{ ...style, top: `${offsetTop}px`, zIndex: 2 }}
        >
          <div className="layout-container mx-auto">{children}</div>
        </div>
      </>
    );
  },
);

interface LayoutHeadlineProps {
  text: string;
  url?: string;
  level?: number;
  className?: string;
}

const LayoutHeadline: React.FC<LayoutHeadlineProps> = ({ className, level = 1, text, url }) => {
  const sanitizedText = sanitizeHTML(text);

  const HeadElement = React.useMemo(() => {
    switch (level) {
      case 1:
      default:
        return H1;
      case 2:
        return H2;
    }
  }, [level]);

  return (
    <HeadElement className={`layout-title ${className ? className : ''}`}>
      {url ? (
        <a className="layout-title__link" href={url}>
          {sanitizedText}
        </a>
      ) : (
        sanitizedText
      )}
    </HeadElement>
  );
};

const LayoutAbove: React.FC<React.PropsWithChildren<{ offsetLeft?: number }>> = ({ children, offsetLeft }) => {
  if (!children) {
    return <></>;
  }
  return (
    <LayoutAboveDiv className="layout-above" offsetLeft={offsetLeft}>
      <div className="layout-container mx-auto">{children}</div>
    </LayoutAboveDiv>
  );
};

const LayoutBelow: React.FC<React.PropsWithChildren> = ({ children }) => {
  if (!children) {
    return <></>;
  }
  return <div className="layout-below w-full">{children}</div>;
};

const LayoutMain: React.FC<React.PropsWithChildren<{ className?: string }>> = React.memo(({ children, className }) => {
  return <div className={`layout-main ${className || ''} flex flex-grow flex-col justify-between`}>{children}</div>;
});

const LayoutSidebar: React.FC<React.PropsWithChildren<{ width?: number; visible: boolean }>> = ({
  children,
  visible,
  width,
}) => {
  return visible ? (
    <LayoutSidebarDiv $width={width} className="layout-sidebar flex-none justify-between">
      {children}
    </LayoutSidebarDiv>
  ) : null;
};

const LayoutLeftSidebar: React.FC<React.PropsWithChildren<{ visible: boolean; sidebarWrapperClass?: string }>> = ({
  children,
  sidebarWrapperClass = '',
  visible = true,
}) => {
  return visible ? (
    <LayoutLeftSidebarDiv className={`layout-left-sidebar ${sidebarWrapperClass} flex-none justify-between `}>
      {children}
    </LayoutLeftSidebarDiv>
  ) : (
    <></>
  );
};

const LayoutFooter: React.FC<React.PropsWithChildren> = ({ children }) => {
  if (!children) {
    return <></>;
  }
  return (
    <div className="layout-footer w-full py-4 overflow-hidden">
      <div className="layout-container mx-auto">{children}</div>
    </div>
  );
};

export const LayoutBox: React.FC<React.PropsWithChildren<{ className?: string }>> = ({ children, className = '' }) => {
  return <div className={classNames('mb-4 w-full', { [`${className}`]: !!className })}>{children}</div>;
};

export const LayoutRow: React.FC<React.PropsWithChildren<{ className?: string }>> = ({ children, className = '' }) => {
  if (!children) {
    return <></>;
  }
  return <div className={`${className} flex`}>{children}</div>;
};

export default Layout;

const NotificationLink = styled.a`
  background: yellow;
  border-radius: 4px;
  font-style: italic;
  font-weight: bold;
  margin-left: 0.35rem;
  padding: 0rem 0.25rem;
`;

export const H1 = styled.h1`
  font-size: 2.6rem;
  line-height: 3.25rem;
  font-weight: bold;
  @media (max-width: 600px) {
    font-size: 1.5rem;
    line-height: 1.9rem;
    font-weight: 800;
  }
`;

const H2 = styled.h2`
  font-size: 1.8rem;
  line-height: 3.25rem;
  font-weight: bold;
  color: ${({ theme }) => theme.colorPalette.gray600};
  @media (max-width: 600px) {
    font-size: 0.9rem;
    line-height: 1.9rem;
    font-weight: 800;
  }
`;

const LayoutWrapper = styled.div`
  margin-top: -1.5px;
  .layout-above {
    .section-description {
      margin-bottom: 1rem;
    }
  }
  .related-info-container {
    display: inline-flex;
    margin: 0.5rem 0;
  }
  .has-left-sidebar {
    @media (min-width: 800px) {
      .layout-sidebar {
        display: none;
      }
    }
    @media (min-width: 1100px) {
      .layout-sidebar {
        display: block;
      }
    }
  }
  .layout-flex {
    @media (min-width: 800px) {
      flex-direction: row;
    }
    > div {
      @media (max-width: 799px) {
        width: 100%;
      }
    }
  }
  .layout-header {
    &.full-width {
      padding: 0;
      .layout-container {
        max-width: 100%;
      }
    }
  }
  .layout-main {
    overflow-x: auto;
    width: 100%;
  }
  @media (min-width: 1300px) {
    .layout-container {
      max-width: 1300px;
    }
  }
  @media (max-width: 1300px) {
    .layout-container,
    .layout-header {
      padding-left: 0.25rem;
      padding-right: 0.25rem;
      &.full-width {
        .layout-container {
          padding: 0;
        }
      }
    }
  }
  /* @media (max-width: 800px) {
    margin-top: 1rem;
  } */
`;

const LayoutContainer = styled.div<{ layoutMaxWidth: string }>`
  ${props => props.layoutMaxWidth}
  margin-top: 1rem;
  margin-bottom: 1.5rem;
`;

const LayoutSidebarDiv = styled.div<{ $width?: number }>`
  width: ${({ $width }) => $width || 300}px;
  @media (min-width: 800px) {
    margin-left: 1rem;
  }
`;

const LayoutLeftSidebarDiv = styled.div`
  display: block;
  width: 250px;
  padding: 0 0.25rem;
  .full-width {
    width: 100%;
    max-width: 100%;
  }
  @media (max-width: 799px) {
    padding: 0 0.725rem 1rem;
    max-width: 100%;
    width: 100%;
  }
`;

const LayoutAboveDiv = styled.div<{ offsetLeft?: number }>`
  &.layout-above {
    width: 100%;
    overflow: hidden;
    margin-bottom: 1rem;
    padding-left: ${({ offsetLeft }) => (offsetLeft ? `${offsetLeft}px` : 'unset')};
    @media (max-width: 800px) {
      padding-left: unset;
    }
  }
`;
