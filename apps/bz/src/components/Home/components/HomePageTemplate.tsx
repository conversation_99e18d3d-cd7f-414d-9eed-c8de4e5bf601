import React from 'react';
import { Layout } from '@benzinga/core-ui';
import { News } from '@benzinga/basic-news-manager';
import { HOME_PAGE_TABS, HomeTabs } from './HomeTabs';
import { MoneyBlocks } from '@benzinga/money';
import { HomeProps } from '../interface';
import styled from '@benzinga/themetron';
import { NoFirstRender } from '@benzinga/hooks';
import { MoneySidebar } from '@benzinga/money';
import { LayoutAdmin } from '@benzinga/blocks';
import { useBenzingaEdge } from '@benzinga/edge';
import StickyBox from 'react-sticky-box';

const HomePageSidebar = React.lazy(() =>
  import('./HomePageSidebar').then(module => ({
    default: module.HomePageSidebar,
  })),
);

interface HomePageTemplateProps extends HomeProps {
  featuredNews?: News[];
  layoutUnderFeatured?: React.ReactNode;
  displayMoreFeaturedNews?: boolean;
}

export const HomePageTemplate: React.FC<HomePageTemplateProps> = ({ ...props }) => {
  const { adLightEnabled } = useBenzingaEdge();

  // For Ad-light users, make the sidebar stickier so the top right-rail ad stays in frame
  const sidebarSettings = React.useMemo(() => {
    if (adLightEnabled) {
      return {
        offsetTop: 0, // Make it stick to the very top for Ad-light users
        offsetBottom: 0, // Make it stick to the very bottom for Ad-light users
      };
    }
    return undefined; // Use default settings for non-Ad-light users
  }, [adLightEnabled]);

  return (
    <Container>
      <Layout
        layoutMain={
          <div className="w-full">
            <NoFirstRender>
              {props?.post && <LayoutAdmin post={props.post} showClearCache={true} type="page" />}
            </NoFirstRender>

            <HomeTabs
              activeTab={props.activeTab ?? HOME_PAGE_TABS.TRENDING}
              tabContent={
                <MoneyBlocks
                  blocks={props?.post?.blocks}
                  disallowLazyLoadBlockList={['acf/raptive-ad-placement', 'acf/nativo-ad-placement']}
                  lazyLoadEnabled={true}
                  lazyLoadStartIndex={5}
                />
              }
            />
          </div>
        }
        layoutSidebar={
          adLightEnabled ? (
            // For Ad-light users, wrap in additional StickyBox for enhanced stickiness
            <StickyBox offsetTop={0} offsetBottom={0}>
              <div className="sidebar-wrapper">
                <React.Suspense fallback={null}>
                  {props?.post?.sidebar ? (
                    <MoneySidebar sidebar={props?.post.sidebar} />
                  ) : (
                    <HomePageSidebar {...props} />
                  )}
                </React.Suspense>
              </div>
            </StickyBox>
          ) : (
            <div className="sidebar-wrapper">
              <React.Suspense fallback={null}>
                {props?.post?.sidebar ? <MoneySidebar sidebar={props?.post.sidebar} /> : <HomePageSidebar {...props} />}
              </React.Suspense>
            </div>
          )
        }
        sidebarSettings={sidebarSettings}
      />
    </Container>
  );
};

const Container = styled.div`
  .sidebar-wrapper .sidebar,
  .sidebar {
    > div > * {
      margin-top: 32px;
    }
    .podcasts-title {
      margin-bottom: 16px;
    }

    .crypto-dot-com-coin-list-wrapper {
      height: 600px;
      margin: 16px auto auto;
      text-align: center;
      width: 300px;
      -webkit-padding-start: 1rem;
      padding-inline-start: 1rem;
      -webkit-padding-end: 1rem;
      padding-inline-end: 1rem;
      padding-top: 1rem;
      padding-bottom: 3rem;
    }

    .livestream-card-wrapper {
      height: 100%;
      margin-top: 61px;

      .livestream-card {
        max-height: 31.5rem;
        max-width: 24rem;
        height: 100%;
        overflow: auto;
      }
    }

    .benzinga-briefs {
      background-color: #fffcde;
      border: none;
      height: 100%;
      max-height: 60vh;
      overflow: hidden;

      .content-feed {
        border: 1px solid #faca15;
        border-radius: 0 0 4px 4px;
        border-top: none;
      }
    }
    .benzinga-briefs-footer {
      margin-top: 8px;
      display: block;

      span {
        text-transform: uppercase;
      }
    }
  }
`;

export default HomePageTemplate;
